local Utils = require("common/Utils")
local Mat3 = require("common/Mat3")
local AETools = require("common/AETools")
local Helper = require("cc/Helper")
local AE = require("AE")


local TRMain = {}
TRMain.__index = TRMain
function TRMain.new ()
    local self =  setmetatable({}, TRMain)
    self.AE = AE
    self.EXPAND = Amaz.Vector2f(1.6, 1.1)
    return self
end

function TRMain:create (env, scene)
    local order = 0
    order = Helper.initPipeline(scene, "@Pipeline1", order)
    order = Helper.initPipeline(scene, "Gaussian_Blur_Root1", order)
    order = Helper.initPipeline(scene, "Gaussian_Blur_Root2", order)
    order = Helper.initPipeline(scene, "Exposure_Root", order)
    order = Helper.initPipeline(scene, "@Pipeline2", order)

    self.rt0 = scene.assetMgr:SyncLoad("rt/rt0.rt")
    self.rt1 = scene.assetMgr:SyncLoad("rt/rt1.rt")

    self.scale = scene:findEntityBy("scaleWipe"):getComponent("MeshRenderer").material
    self.motion = scene:findEntityBy("motion"):getComponent("MeshRenderer").material
    self.crop = scene:findEntityBy("crop"):getComponent("MeshRenderer").material

    self.blur1Comp = scene:findEntityBy("Gaussian_Blur_Root1"):getComponent("ScriptComponent")
    self.blur2Comp = scene:findEntityBy("Gaussian_Blur_Root2"):getComponent("ScriptComponent")
    self.exposureComp = scene:findEntityBy("Exposure_Root"):getComponent("ScriptComponent")

    self.ae = AETools.new(self.AE)
    self.ae:setAnimFrameRange(19, 43)
end

function TRMain:layout (env, w, h)
    local ew = w * self.EXPAND.x
    local eh = h * self.EXPAND.y
    self.rt0.width = ew
    self.rt0.height = eh
    self.rt1.width = ew
    self.rt1.height = eh

    local s = w / 2
    local vw = w / s
    local vh = h / s
    self.w1 = vw
    self.h1 = vh
    self.scale:setVec2("u_screenSize", Amaz.Vector2f(vw, vh))

    s = math.min(w, h) / 720
    vw = w / s
    vh = h / s
    ew = ew / s
    eh = eh / s
    self.w = vw
    self.h = vh
    self.ew = ew
    self.eh = eh
    self.motion:setVec2("u_screenSize", Amaz.Vector2f(ew, eh))
    self.motion:setVec2("u_spriteSize", Amaz.Vector2f(vw, vh))
    self.crop:setVec2("u_screenSize", Amaz.Vector2f(vw, vh))
    self.crop:setVec2("u_spriteSize", Amaz.Vector2f(ew, eh))
end

function TRMain:update (env, tex0, tex1, progress)
    local useA = progress < Utils.step(19, 43, 32)
    self.scale:setTex("_MainTex", useA and tex0 or tex1)
    self.motion:setTex("u_tex1", useA and tex0 or tex1)

    if useA then
        self.scale:setVec2("u_center", Amaz.Vector2f(self.w1 * 0, self.h1 * 0.5))
        self.scale:setFloat("u_direction", 0)

        local x1 = self.ae:GetVal("Position_A", progress)[1] / 720
        local x2 = x1 + 1
        self.motion:setVec2("u_position1", self:tr(x1, 0.5))
        self.motion:setVec2("u_position2", self:tr(x2, 0.5))
    else
        self.scale:setVec2("u_center", Amaz.Vector2f(self.w1 * 1, self.h1 * 0.5))
        self.scale:setFloat("u_direction", 180)

        local x1 = self.ae:GetVal("Position_B", progress)[1] / 720
        local x2 = x1 - 1
        self.motion:setVec2("u_position1", self:tr(x1, 0.5))
        self.motion:setVec2("u_position2", self:tr(x2, 0.5))
    end

    if not self.blur1 then
        self.blur1 = Helper.getLuaObject(self.blur1Comp)
    end
    if self.blur1 then
        self.blur1.intensity = self.ae:GetVal("Blur1", progress)[1] * 0.5 * 0.7
    end

    if not self.blur2 then
        self.blur2 = Helper.getLuaObject(self.blur2Comp)
    end
    if self.blur2 then
        self.blur2.intensity = self.ae:GetVal("Blur2", progress)[1] * 0.5 * 0.7
    end

    if not self.exposure then
        self.exposure = Helper.getLuaObject(self.exposureComp)
    end
    if self.exposure then
        self.exposure.exposure = self.ae:GetVal("Exposure", progress)[1]
    end
end

function TRMain:tr (x, y)
    local expand = self.EXPAND
    local L = 0.5 - expand.x * 0.5
    local R = 0.5 + expand.x * 0.5
    local B = 0.5 - expand.y * 0.5
    local T = 0.5 + expand.y * 0.5
    x = (x - L) / (R - L)
    y = (y - B) / (T - B)
    return Amaz.Vector2f(x, y)
end

return TRMain