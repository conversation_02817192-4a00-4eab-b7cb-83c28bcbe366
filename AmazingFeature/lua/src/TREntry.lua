local TRMain = require("TRMain")

local TransitionScript = TransitionScript or {}
TransitionScript.__index = TransitionScript

function TransitionScript.new ()
    local self = setmetatable({}, TransitionScript)
    self.w = 0
    self.h = 0
    self.t = 0
    self.main = TRMain.new(self)
    return self
end

function TransitionScript:onStart (comp)
    self.scene = comp.entity.scene
    self.w, self.h = self:_solveTex()

    self.main:create(self, self.scene)
    self.main:layout(self, self.w, self.h)
    self.main:update(self, self.tex0, self.tex1, 0)
end

function TransitionScript:onUpdate (comp, dt)
    local w, h = self:_solveTex()
    if w ~= self.w or h ~= self.h then
        self.w = w
        self.h = h
        self.main:layout(self, w, h)
    end
    ---#ifdef DEV
--//    self.main:update(self, self.tex0, self.tex1, self.t)

    ---#else
        self.main:update(self, self.tex0, self.tex1, Amaz.Input.frameTimestamp)
    ---#endif
end

---#ifdef DEV
--//function TransitionScript:onEvent (comp, event)
--//    if event.type == Amaz.EventType.TOUCH then
--//        local touch = event.args:get(0)
--//        if touch.type == Amaz.TouchType.TOUCH_BEGAN or touch.type == Amaz.TouchType.TOUCH_MOVED then
--//            self.t = touch.x
--//        end
--//    end
--//end
---#endif


function TransitionScript:_solveTex ()
    ---#ifdef DEV
--//    --if self.__ignore == nil then
--//    --    self.__ignore = 1
--//    --end
--//    --if self.__ignore > 0 then
--//    --    self.__ignore = 0
--//    --    local w = Amaz.BuiltinObject.getInputTextureWidth()
--//    --    local h = Amaz.BuiltinObject.getInputTextureHeight()
--//    --    local asset = self.scene.assetMgr
--//    --    if w/h < (9/16 + 3/4) * 0.5 then
--//    --        self.tex0 = asset:SyncLoad("dev/720x1280_100.png")
--//    --        self.tex1 = asset:SyncLoad("dev/720x1280_101.png")
--//    --    elseif w/h < (3/4 + 1/1) * 0.5 then
--//    --        self.tex0 = asset:SyncLoad("dev/720x960_000.png")
--//    --        self.tex1 = asset:SyncLoad("dev/720x960_001.png")
--//    --    elseif w/h < (1/1 + 4/3) * 0.5 then
--//    --        self.tex0 = asset:SyncLoad("dev/720x720_000.png")
--//    --        self.tex1 = asset:SyncLoad("dev/720x720_001.png")
--//    --    elseif w/h < (4/3 + 16/9) * 0.5 then
--//    --        self.tex0 = asset:SyncLoad("dev/960x720_000.png")
--//    --        self.tex1 = asset:SyncLoad("dev/960x720_001.png")
--//    --    else
--//    --        self.tex0 = asset:SyncLoad("dev/1280x720_000.png")
--//    --        self.tex1 = asset:SyncLoad("dev/1280x720_001.png")
--//    --    end
--//    --    if self.tex0 and self.tex1 and self.tex0.width > 1 and self.tex0.height > 1 and self.tex1.width > 1 and self.tex1.height > 1 then
--//    --        self.__ignore = 1
--//    --    end
--//    --end
    ---#else
        self.tex0 = Amaz.BuiltinObject.getUserTexture("#TransitionInput0")
        self.tex1 = Amaz.BuiltinObject.getUserTexture("#TransitionInput1")
    ---#endif

    if not self.tex0 or not self.tex1 then
        self.tex0 = self.scene.assetMgr:SyncLoad("share://input.texture")
        self.tex1 = self.tex0
    end
    return self.tex0.width, self.tex0.height
end

local exports = exports or {}
exports.TransitionScript = TransitionScript
return exports
