-- PreviewGenerator 测试文件
local PreviewGenerator = require("AmazingFeature/lua/PreviewGenerator")
local PreviewExample = require("AmazingFeature/lua/PreviewGeneratorExample")

local TestPreviewGenerator = {}

-- 模拟场景对象
local function createMockScene()
    local mockScene = {}
    
    -- 模拟资源管理器
    mockScene.assetMgr = {
        SyncLoad = function(self, path)
            print("加载纹理: " .. path)
            return {
                width = 512,
                height = 512,
                path = path
            }
        end
    }
    
    -- 模拟实体查找
    mockScene.findEntityBy = function(self, name)
        print("查找实体: " .. name)
        return {
            getComponent = function(self, componentType)
                if componentType == "MeshRenderer" then
                    return {
                        material = {
                            setTex = function(self, paramName, texture)
                                print("设置材质纹理: " .. paramName .. " -> " .. (texture.path or "unknown"))
                            end
                        }
                    }
                elseif componentType == "ScriptComponent" then
                    return {
                        getScript = function(self)
                            return {
                                intensity = 0.5,
                                exposure = 1.0
                            }
                        end
                    }
                end
            end
        }
    end
    
    -- 模拟渲染
    mockScene.render = function(self)
        print("渲染场景")
    end
    
    return mockScene
end

-- 模拟AE数据
local function createMockAEData()
    return {
        Blur1 = {
            {{0.1, 0.2, 0.3, 0.4}, {0, 100}, {{0.5}, {1.0}}}
        },
        Blur2 = {
            {{0.1, 0.2, 0.3, 0.4}, {0, 100}, {{0.3}, {0.8}}}
        },
        Exposure = {
            {{0.1, 0.2, 0.3, 0.4}, {0, 100}, {{0.8}, {1.2}}}
        }
    }
end

-- 测试1: 基本功能测试
function TestPreviewGenerator.testBasicGeneration()
    print("\n=== 测试基本预览图生成 ===")
    
    local scene = createMockScene()
    local generator = PreviewGenerator:new()
    generator.AE = createMockAEData()
    
    -- 模拟保存和合成函数
    generator.saveFrame = function(self, texture, path)
        print("保存帧: " .. path)
        return true
    end
    
    generator.combineFrames = function(self, outputPath, frameFiles, config)
        print("合成预览图: " .. outputPath .. "/preview." .. config.outputFormat)
        print("使用 " .. #frameFiles .. " 个帧文件")
        return outputPath .. "/preview." .. config.outputFormat
    end
    
    local outputPath = "/tmp/test_basic"
    local result = generator:generatePreview(scene, outputPath)
    
    if result then
        print("✓ 基本预览图生成测试通过")
        return true
    else
        print("✗ 基本预览图生成测试失败")
        return false
    end
end

-- 测试2: 自定义配置测试
function TestPreviewGenerator.testCustomConfig()
    print("\n=== 测试自定义配置 ===")
    
    local scene = createMockScene()
    local customConfig = {
        frameCount = 6,
        outputSize = {width = 800, height = 600},
        inputTextures = {"test1.png", "test2.png"},
        gridLayout = {rows = 2, cols = 3},
        outputFormat = "jpg"
    }
    
    local generator = PreviewGenerator:new(customConfig)
    generator.AE = createMockAEData()
    
    -- 验证配置是否正确应用
    assert(generator.config.frameCount == 6, "帧数配置错误")
    assert(generator.config.outputSize.width == 800, "输出宽度配置错误")
    assert(generator.config.outputFormat == "jpg", "输出格式配置错误")
    
    print("✓ 自定义配置测试通过")
    return true
end

-- 测试3: 错误处理测试
function TestPreviewGenerator.testErrorHandling()
    print("\n=== 测试错误处理 ===")
    
    local scene = createMockScene()
    
    -- 模拟纹理加载失败
    scene.assetMgr.SyncLoad = function(self, path)
        if path == "nonexistent.png" then
            return nil
        end
        return {width = 512, height = 512, path = path}
    end
    
    local generator = PreviewGenerator:new({
        inputTextures = {"nonexistent.png", "test.png"}
    })
    generator.AE = createMockAEData()
    
    local result = generator:generatePreview(scene, "/tmp/test_error")
    
    if not result then
        print("✓ 错误处理测试通过 - 正确处理了纹理加载失败")
        return true
    else
        print("✗ 错误处理测试失败 - 应该返回false")
        return false
    end
end

-- 测试4: 便捷函数测试
function TestPreviewGenerator.testConvenienceFunctions()
    print("\n=== 测试便捷函数 ===")
    
    local scene = createMockScene()
    local generator = PreviewGenerator:new()
    generator.AE = createMockAEData()
    
    -- 模拟函数
    generator.generatePreview = function(self, scene, outputPath, config)
        print("调用generatePreview，配置:")
        if config then
            print("  frameCount: " .. (config.frameCount or "default"))
            print("  outputSize: " .. (config.outputSize and (config.outputSize.width .. "x" .. config.outputSize.height) or "default"))
        end
        return outputPath .. "/preview.png"
    end
    
    -- 测试快速预览
    local quickResult = generator:quickPreview(scene, "/tmp/quick")
    assert(quickResult, "快速预览失败")
    
    -- 测试高质量预览
    local hqResult = generator:highQualityPreview(scene, "/tmp/hq")
    assert(hqResult, "高质量预览失败")
    
    print("✓ 便捷函数测试通过")
    return true
end

-- 运行所有测试
function TestPreviewGenerator.runAllTests()
    print("开始运行PreviewGenerator测试...")
    
    local tests = {
        TestPreviewGenerator.testBasicGeneration,
        TestPreviewGenerator.testCustomConfig,
        TestPreviewGenerator.testErrorHandling,
        TestPreviewGenerator.testConvenienceFunctions
    }
    
    local passed = 0
    local total = #tests
    
    for i, test in ipairs(tests) do
        local success, error = pcall(test)
        if success then
            passed = passed + 1
        else
            print("✗ 测试失败: " .. tostring(error))
        end
    end
    
    print(string.format("\n测试结果: %d/%d 通过", passed, total))
    
    if passed == total then
        print("🎉 所有测试通过!")
    else
        print("❌ 部分测试失败")
    end
    
    return passed == total
end

-- 如果直接运行此文件，执行测试
if ... == nil then
    TestPreviewGenerator.runAllTests()
end

return TestPreviewGenerator
