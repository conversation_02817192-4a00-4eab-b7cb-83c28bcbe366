local AETools = require("AmazingFeature/lua/src/common/AETools")
local Helper = require("AmazingFeature/lua/src/cc/Helper")

local PreviewGenerator = {}

function PreviewGenerator:generatePreview(scene, outputPath, frameCount)
    -- 初始化资源
    local tex0 = scene.assetMgr:SyncLoad("dev/preview_input0.png")
    local tex1 = scene.assetMgr:SyncLoad("dev/preview_input1.png")
    
    -- 设置渲染管线
    local order = 0
    order = Helper.initPipeline(scene, "@Pipeline1", order)
    order = Helper.initPipeline(scene, "Gaussian_Blur_Root1", order)
    order = Helper.initPipeline(scene, "Gaussian_Blur_Root2", order)
    order = Helper.initPipeline(scene, "Exposure_Root", order)
    order = Helper.initPipeline(scene, "@Pipeline2", order)
    
    -- 初始化AE工具
    local ae = AETools.new(self.AE)
    ae:setAnimFrameRange(19, 43)
    
    -- 创建输出纹理
    local outputTex = Amaz.RenderTexture.new()
    outputTex.width = tex0.width
    outputTex.height = tex0.height
    
    -- 生成预览帧
    for i = 0, frameCount - 1 do
        local progress = i / (frameCount - 1)
        self:renderFrame(scene, tex0, tex1, progress, ae)
        self:saveFrame(outputTex, outputPath .. "/frame_" .. i .. ".png")
    end
    
    -- 合成最终预览图
    self:combineFrames(outputPath, frameCount)
end

function PreviewGenerator:renderFrame(scene, tex0, tex1, progress, ae)
    -- 获取材质
    local scale = scene:findEntityBy("scaleWipe"):getComponent("MeshRenderer").material
    local motion = scene:findEntityBy("motion"):getComponent("MeshRenderer").material
    local crop = scene:findEntityBy("crop"):getComponent("MeshRenderer").material
    
    -- 设置材质参数
    local useA = progress < 0.5
    scale:setTex("_MainTex", useA and tex0 or tex1)
    motion:setTex("u_tex1", useA and tex0 or tex1)
    
    -- 应用模糊效果
    local blur1Comp = scene:findEntityBy("Gaussian_Blur_Root1"):getComponent("ScriptComponent")
    local blur2Comp = scene:findEntityBy("Gaussian_Blur_Root2"):getComponent("ScriptComponent")
    local exposureComp = scene:findEntityBy("Exposure_Root"):getComponent("ScriptComponent")
    
    local blur1 = Helper.getLuaObject(blur1Comp)
    local blur2 = Helper.getLuaObject(blur2Comp)
    local exposure = Helper.getLuaObject(exposureComp)
    
    if blur1 then
        blur1.intensity = ae:GetVal("Blur1", progress)[1] * 0.5 * 0.7
    end
    
    if blur2 then
        blur2.intensity = ae:GetVal("Blur2", progress)[1] * 0.5 * 0.7
    end
    
    if exposure then
        exposure.exposure = ae:GetVal("Exposure", progress)[1]
    end
end

function PreviewGenerator:saveFrame(texture, path)
    -- 保存纹理到文件
    Amaz.TextureUtils.saveTextureToFile(texture, path)
end

function PreviewGenerator:combineFrames(outputPath, frameCount)
    -- 使用系统命令或内置函数合并帧为一张预览图
    local cmd = "magick montage " .. outputPath .. "/frame_*.png -tile " .. math.ceil(math.sqrt(frameCount)) .. "x -geometry +0+0 " .. outputPath .. "/preview.png"
    os.execute(cmd)
end

return PreviewGenerator