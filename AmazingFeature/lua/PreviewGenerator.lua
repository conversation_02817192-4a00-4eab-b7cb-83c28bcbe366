local AETools = require("AmazingFeature/lua/src/common/AETools")
local Helper = require("AmazingFeature/lua/src/cc/Helper")

local PreviewGenerator = {}

-- 默认配置
local DEFAULT_CONFIG = {
    frameCount = 9,
    outputSize = {width = 512, height = 512},
    inputTextures = {"dev/preview_input0.png", "dev/preview_input1.png"},
    pipelines = {"@Pipeline1", "Gaussian_Blur_Root1", "Gaussian_Blur_Root2", "Exposure_Root", "@Pipeline2"},
    animFrameRange = {19, 43},
    gridLayout = {rows = 3, cols = 3},
    outputFormat = "png"
}

function PreviewGenerator:new(config)
    local instance = {}
    setmetatable(instance, self)
    self.__index = self

    -- 合并配置
    instance.config = {}
    for k, v in pairs(DEFAULT_CONFIG) do
        instance.config[k] = config and config[k] or v
    end

    return instance
end

function PreviewGenerator:generatePreview(scene, outputPath, customConfig)
    -- 使用自定义配置覆盖默认配置
    local config = self.config
    if customConfig then
        for k, v in pairs(customConfig) do
            config[k] = v
        end
    end

    -- 初始化资源
    local textures = {}
    for i, texPath in ipairs(config.inputTextures) do
        textures[i] = scene.assetMgr:SyncLoad(texPath)
        if not textures[i] then
            print("警告: 无法加载纹理 " .. texPath)
            return false
        end
    end

    -- 设置渲染管线
    local order = 0
    for _, pipelineName in ipairs(config.pipelines) do
        order = Helper.initPipeline(scene, pipelineName, order)
    end

    -- 初始化AE工具
    local ae = AETools.new(self.AE)
    ae:setAnimFrameRange(config.animFrameRange[1], config.animFrameRange[2])

    -- 创建输出纹理
    local outputTex = Amaz.RenderTexture.new()
    outputTex.width = config.outputSize.width
    outputTex.height = config.outputSize.height

    -- 生成预览帧
    local frameFiles = {}
    for i = 0, config.frameCount - 1 do
        local progress = i / (config.frameCount - 1)
        self:renderFrame(scene, textures, progress, ae, config)
        local frameFile = outputPath .. "/frame_" .. i .. "." .. config.outputFormat
        self:saveFrame(outputTex, frameFile)
        table.insert(frameFiles, frameFile)
    end

    -- 合成最终预览图
    return self:combineFrames(outputPath, frameFiles, config)
end

function PreviewGenerator:renderFrame(scene, textures, progress, ae, config)
    -- 获取材质组件
    local materials = self:getMaterials(scene)

    -- 设置材质参数
    self:setMaterialTextures(materials, textures, progress)

    -- 应用效果参数
    self:applyEffects(scene, ae, progress, config)

    -- 渲染当前帧
    scene:render()
end

function PreviewGenerator:getMaterials(scene)
    local materials = {}

    -- 获取常用材质
    local scaleEntity = scene:findEntityBy("scaleWipe")
    if scaleEntity then
        materials.scale = scaleEntity:getComponent("MeshRenderer").material
    end

    local motionEntity = scene:findEntityBy("motion")
    if motionEntity then
        materials.motion = motionEntity:getComponent("MeshRenderer").material
    end

    local cropEntity = scene:findEntityBy("crop")
    if cropEntity then
        materials.crop = cropEntity:getComponent("MeshRenderer").material
    end

    return materials
end

function PreviewGenerator:setMaterialTextures(materials, textures, progress)
    if not textures or #textures < 2 then
        return
    end

    local tex0, tex1 = textures[1], textures[2]
    local useA = progress < 0.5

    -- 设置主纹理
    if materials.scale then
        materials.scale:setTex("_MainTex", useA and tex0 or tex1)
    end

    if materials.motion then
        materials.motion:setTex("u_tex1", useA and tex0 or tex1)
    end

    -- 可以根据需要添加更多材质设置
    if materials.crop then
        materials.crop:setTex("_MainTex", useA and tex0 or tex1)
    end
end

function PreviewGenerator:applyEffects(scene, ae, progress, config)
    -- 应用模糊效果
    self:applyBlurEffect(scene, ae, progress, "Gaussian_Blur_Root1", "Blur1")
    self:applyBlurEffect(scene, ae, progress, "Gaussian_Blur_Root2", "Blur2")

    -- 应用曝光效果
    self:applyExposureEffect(scene, ae, progress, "Exposure_Root", "Exposure")

    -- 可以根据配置添加更多效果
end

function PreviewGenerator:applyBlurEffect(scene, ae, progress, entityName, paramName)
    local blurComp = scene:findEntityBy(entityName):getComponent("ScriptComponent")
    if blurComp then
        local blur = Helper.getLuaObject(blurComp)
        if blur then
            local blurValue = ae:GetVal(paramName, progress)
            if blurValue then
                blur.intensity = blurValue[1] * 0.5 * 0.7
            end
        end
    end
end

function PreviewGenerator:applyExposureEffect(scene, ae, progress, entityName, paramName)
    local exposureComp = scene:findEntityBy(entityName):getComponent("ScriptComponent")
    if exposureComp then
        local exposure = Helper.getLuaObject(exposureComp)
        if exposure then
            local exposureValue = ae:GetVal(paramName, progress)
            if exposureValue then
                exposure.exposure = exposureValue[1]
            end
        end
    end
end

function PreviewGenerator:saveFrame(texture, path)
    -- 确保输出目录存在
    local dir = path:match("(.*/)")
    if dir then
        os.execute("mkdir -p " .. dir)
    end

    -- 保存纹理到文件
    local success = Amaz.TextureUtils.saveTextureToFile(texture, path)
    if not success then
        print("警告: 保存帧失败 " .. path)
    end
    return success
end

function PreviewGenerator:combineFrames(outputPath, frameFiles, config)
    local previewFile = outputPath .. "/preview." .. config.outputFormat

    -- 方法1: 使用ImageMagick (如果可用)
    if self:tryImageMagickCombine(frameFiles, previewFile, config) then
        return previewFile
    end

    -- 方法2: 使用内置函数合成
    if self:tryBuiltinCombine(frameFiles, previewFile, config) then
        return previewFile
    end

    -- 方法3: 简单复制第一帧作为预览
    if #frameFiles > 0 then
        os.execute("cp " .. frameFiles[1] .. " " .. previewFile)
        return previewFile
    end

    return nil
end

function PreviewGenerator:tryImageMagickCombine(frameFiles, outputFile, config)
    -- 检查ImageMagick是否可用
    local checkCmd = "which magick >/dev/null 2>&1"
    if os.execute(checkCmd) ~= 0 then
        return false
    end

    -- 构建montage命令
    local fileList = table.concat(frameFiles, " ")
    local tileLayout = config.gridLayout.cols .. "x" .. config.gridLayout.rows
    local cmd = string.format("magick montage %s -tile %s -geometry +2+2 -background white %s",
                             fileList, tileLayout, outputFile)

    local result = os.execute(cmd)
    return result == 0
end

function PreviewGenerator:tryBuiltinCombine(frameFiles, outputFile, config)
    -- 尝试使用引擎内置的图像合成功能
    if not Amaz.ImageComposer then
        return false
    end

    local composer = Amaz.ImageComposer.new()
    composer:setGridLayout(config.gridLayout.rows, config.gridLayout.cols)
    composer:setOutputSize(config.outputSize.width, config.outputSize.height)

    for i, frameFile in ipairs(frameFiles) do
        composer:addImage(frameFile)
    end

    return composer:saveToFile(outputFile)
end

-- 便捷函数：快速生成预览图
function PreviewGenerator:quickPreview(scene, outputPath, inputTextures)
    local config = {
        frameCount = 4,
        inputTextures = inputTextures or {"dev/preview_input0.png", "dev/preview_input1.png"},
        gridLayout = {rows = 2, cols = 2}
    }

    return self:generatePreview(scene, outputPath, config)
end

-- 便捷函数：生成高质量预览图
function PreviewGenerator:highQualityPreview(scene, outputPath, inputTextures)
    local config = {
        frameCount = 16,
        outputSize = {width = 1024, height = 1024},
        inputTextures = inputTextures or {"dev/preview_input0.png", "dev/preview_input1.png"},
        gridLayout = {rows = 4, cols = 4}
    }

    return self:generatePreview(scene, outputPath, config)
end

return PreviewGenerator