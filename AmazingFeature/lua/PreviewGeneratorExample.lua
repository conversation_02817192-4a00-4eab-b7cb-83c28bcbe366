-- PreviewGenerator 使用示例
local PreviewGenerator = require("AmazingFeature/lua/PreviewGenerator")

local PreviewExample = {}

-- 示例1: 基本预览图生成
function PreviewExample.basicPreview(scene)
    -- 创建预览生成器实例
    local generator = PreviewGenerator:new()
    
    -- 生成基本预览图
    local outputPath = "/tmp/preview_output"
    local success = generator:generatePreview(scene, outputPath)
    
    if success then
        print("预览图生成成功: " .. success)
    else
        print("预览图生成失败")
    end
    
    return success
end

-- 示例2: 自定义配置预览图
function PreviewExample.customPreview(scene)
    -- 自定义配置
    local customConfig = {
        frameCount = 12,
        outputSize = {width = 800, height = 600},
        inputTextures = {"assets/texture1.png", "assets/texture2.png"},
        gridLayout = {rows = 3, cols = 4},
        outputFormat = "jpg"
    }
    
    local generator = PreviewGenerator:new(customConfig)
    local outputPath = "/tmp/custom_preview"
    
    return generator:generatePreview(scene, outputPath)
end

-- 示例3: 快速预览（低质量，快速生成）
function PreviewExample.quickPreview(scene)
    local generator = PreviewGenerator:new()
    local outputPath = "/tmp/quick_preview"
    local inputTextures = {"dev/preview_input0.png", "dev/preview_input1.png"}
    
    return generator:quickPreview(scene, outputPath, inputTextures)
end

-- 示例4: 高质量预览
function PreviewExample.highQualityPreview(scene)
    local generator = PreviewGenerator:new()
    local outputPath = "/tmp/hq_preview"
    local inputTextures = {"dev/preview_input0.png", "dev/preview_input1.png"}
    
    return generator:highQualityPreview(scene, outputPath, inputTextures)
end

-- 示例5: 批量生成多种预览图
function PreviewExample.batchPreview(scene)
    local generator = PreviewGenerator:new()
    local results = {}
    
    -- 生成不同配置的预览图
    local configs = {
        {
            name = "thumbnail",
            config = {
                frameCount = 4,
                outputSize = {width = 256, height = 256},
                gridLayout = {rows = 2, cols = 2}
            }
        },
        {
            name = "medium",
            config = {
                frameCount = 9,
                outputSize = {width = 512, height = 512},
                gridLayout = {rows = 3, cols = 3}
            }
        },
        {
            name = "large",
            config = {
                frameCount = 16,
                outputSize = {width = 1024, height = 1024},
                gridLayout = {rows = 4, cols = 4}
            }
        }
    }
    
    for _, item in ipairs(configs) do
        local outputPath = "/tmp/batch_preview_" .. item.name
        local success = generator:generatePreview(scene, outputPath, item.config)
        results[item.name] = success
        
        if success then
            print("生成 " .. item.name .. " 预览图成功: " .. success)
        else
            print("生成 " .. item.name .. " 预览图失败")
        end
    end
    
    return results
end

-- 示例6: 带进度回调的预览生成
function PreviewExample.previewWithProgress(scene, progressCallback)
    local generator = PreviewGenerator:new()
    
    -- 重写生成方法以支持进度回调
    local originalGenerate = generator.generatePreview
    generator.generatePreview = function(self, scene, outputPath, customConfig)
        local config = self.config
        if customConfig then
            for k, v in pairs(customConfig) do
                config[k] = v
            end
        end
        
        -- 初始化资源
        local textures = {}
        for i, texPath in ipairs(config.inputTextures) do
            textures[i] = scene.assetMgr:SyncLoad(texPath)
            if not textures[i] then
                print("警告: 无法加载纹理 " .. texPath)
                return false
            end
        end
        
        if progressCallback then
            progressCallback(0, "初始化完成")
        end
        
        -- 设置渲染管线
        local order = 0
        for _, pipelineName in ipairs(config.pipelines) do
            order = Helper.initPipeline(scene, pipelineName, order)
        end
        
        -- 初始化AE工具
        local ae = AETools.new(self.AE)
        ae:setAnimFrameRange(config.animFrameRange[1], config.animFrameRange[2])
        
        -- 创建输出纹理
        local outputTex = Amaz.RenderTexture.new()
        outputTex.width = config.outputSize.width
        outputTex.height = config.outputSize.height
        
        -- 生成预览帧
        local frameFiles = {}
        for i = 0, config.frameCount - 1 do
            local progress = i / (config.frameCount - 1)
            self:renderFrame(scene, textures, progress, ae, config)
            local frameFile = outputPath .. "/frame_" .. i .. "." .. config.outputFormat
            self:saveFrame(outputTex, frameFile)
            table.insert(frameFiles, frameFile)
            
            if progressCallback then
                local overallProgress = (i + 1) / config.frameCount * 0.8  -- 80% for frame generation
                progressCallback(overallProgress, "生成帧 " .. (i + 1) .. "/" .. config.frameCount)
            end
        end
        
        if progressCallback then
            progressCallback(0.9, "合成预览图...")
        end
        
        -- 合成最终预览图
        local result = self:combineFrames(outputPath, frameFiles, config)
        
        if progressCallback then
            progressCallback(1.0, result and "完成" or "失败")
        end
        
        return result
    end
    
    local outputPath = "/tmp/progress_preview"
    return generator:generatePreview(scene, outputPath)
end

-- 工具函数：清理临时文件
function PreviewExample.cleanup(outputPath)
    if outputPath then
        os.execute("rm -rf " .. outputPath)
        print("清理临时文件: " .. outputPath)
    end
end

return PreviewExample
