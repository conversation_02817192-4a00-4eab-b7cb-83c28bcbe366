# PreviewGenerator - 预览图生成器

## 概述

PreviewGenerator 是一个用于生成特效预览图的 Lua 模块。它可以根据场景和配置参数，生成多帧预览图并合成为一张网格布局的预览图。

## 特性

- 🎨 **灵活配置**: 支持自定义帧数、输出尺寸、网格布局等
- 🚀 **多种预设**: 提供快速预览和高质量预览等便捷函数
- 🔧 **错误处理**: 完善的错误处理和日志输出
- 📦 **批量生成**: 支持一次生成多种规格的预览图
- 🎯 **进度回调**: 支持生成进度监控

## 快速开始

### 基本用法

```lua
local PreviewGenerator = require("AmazingFeature/lua/PreviewGenerator")

-- 创建生成器实例
local generator = PreviewGenerator:new()

-- 生成预览图
local success = generator:generatePreview(scene, "/path/to/output")
if success then
    print("预览图生成成功: " .. success)
end
```

### 自定义配置

```lua
local customConfig = {
    frameCount = 12,                    -- 生成12帧
    outputSize = {width = 800, height = 600},  -- 输出尺寸
    inputTextures = {"tex1.png", "tex2.png"},  -- 输入纹理
    gridLayout = {rows = 3, cols = 4},  -- 3x4网格布局
    outputFormat = "jpg"                -- 输出格式
}

local generator = PreviewGenerator:new(customConfig)
local result = generator:generatePreview(scene, "/path/to/output")
```

## 配置选项

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `frameCount` | number | 9 | 生成的帧数 |
| `outputSize` | table | {width=512, height=512} | 输出图像尺寸 |
| `inputTextures` | array | {"dev/preview_input0.png", "dev/preview_input1.png"} | 输入纹理路径 |
| `pipelines` | array | {"@Pipeline1", "Gaussian_Blur_Root1", ...} | 渲染管线名称 |
| `animFrameRange` | array | {19, 43} | 动画帧范围 |
| `gridLayout` | table | {rows=3, cols=3} | 网格布局 |
| `outputFormat` | string | "png" | 输出格式 (png/jpg) |

## 便捷函数

### 快速预览 (低质量，快速生成)

```lua
local generator = PreviewGenerator:new()
local result = generator:quickPreview(scene, "/path/to/output", inputTextures)
```

### 高质量预览

```lua
local generator = PreviewGenerator:new()
local result = generator:highQualityPreview(scene, "/path/to/output", inputTextures)
```

## 批量生成示例

```lua
local PreviewExample = require("AmazingFeature/lua/PreviewGeneratorExample")

-- 生成多种规格的预览图
local results = PreviewExample.batchPreview(scene)

-- 结果包含:
-- results.thumbnail - 缩略图 (256x256, 2x2网格)
-- results.medium    - 中等尺寸 (512x512, 3x3网格)  
-- results.large     - 大尺寸 (1024x1024, 4x4网格)
```

## 进度监控

```lua
local function progressCallback(progress, message)
    print(string.format("进度: %.1f%% - %s", progress * 100, message))
end

local result = PreviewExample.previewWithProgress(scene, progressCallback)
```

## 错误处理

生成器包含完善的错误处理机制：

- 纹理加载失败检测
- 输出目录自动创建
- 多种合成方法回退 (ImageMagick -> 内置函数 -> 简单复制)

```lua
local generator = PreviewGenerator:new()
local result = generator:generatePreview(scene, outputPath)

if not result then
    print("预览图生成失败，请检查:")
    print("1. 输入纹理是否存在")
    print("2. 输出路径是否可写")
    print("3. 场景实体是否正确")
end
```

## 测试

运行测试以验证功能：

```lua
local TestPreviewGenerator = require("AmazingFeature/lua/test_preview_generator")
TestPreviewGenerator.runAllTests()
```

## 依赖

- `AmazingFeature/lua/src/common/AETools` - AE动画工具
- `AmazingFeature/lua/src/cc/Helper` - 辅助函数
- `Amaz.RenderTexture` - 渲染纹理
- `Amaz.TextureUtils` - 纹理工具

## 注意事项

1. **纹理路径**: 确保输入纹理文件存在且可访问
2. **输出权限**: 确保输出目录有写入权限
3. **内存管理**: 大量帧生成时注意内存使用
4. **ImageMagick**: 如需高质量合成，建议安装ImageMagick

## 扩展

可以通过继承或修改以下方法来扩展功能：

- `renderFrame()` - 自定义渲染逻辑
- `applyEffects()` - 添加新的特效
- `combineFrames()` - 自定义合成方法

## 示例输出

生成的预览图将包含：
- 多个时间点的特效帧
- 网格布局排列
- 统一的输出尺寸
- 可选的背景和边框

典型的3x3网格预览图展示了特效从开始到结束的9个关键帧。
