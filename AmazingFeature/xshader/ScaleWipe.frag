precision highp float;

uniform sampler2D _MainTex;
uniform vec2 u_screenSize;
uniform vec2 u_center;
uniform float u_stretch;
uniform float u_direction;

varying vec2 v_uv;

#define PI 3.14159265359


vec2 ScaleWipe (vec2 screenSize, vec2 center, float direction, float stretch, vec2 uv) {
    direction = radians(direction);
    vec2 P = v_uv * screenSize;
    vec2 N = vec2(cos(direction), sin(direction));
    vec2 V = P - center;
    float y = dot(V, N);
    // y = x + stretch * x^2
    // 0 = stretch * x^2 + x - y
    // x = (-1 +- sqrt(1*1 - 4*stretch*-y) ) / (2*stretch)
    float xpos = (-1.0 + sqrt(1.0 + 4.0 * stretch * y)) / (stretch + stretch);
    float xneg = (-1.0 - sqrt(1.0 + 4.0 * stretch * y)) / (stretch + stretch);
    float x = xpos;
    vec2 p = P - N * (y - x);
    p = mix(P, p, step(0.0, y));
    return p / screenSize;
}

void main () {
    vec2 uv = ScaleWipe(u_screenSize, u_center, u_direction, u_stretch, v_uv);
    vec4 base = texture2D(_MainTex, vec2(1.0 - uv.x, uv.y));
    gl_FragColor = base;
}