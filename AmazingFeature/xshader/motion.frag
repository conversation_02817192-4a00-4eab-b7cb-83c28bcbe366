precision highp float;

uniform sampler2D u_tex1;
uniform sampler2D u_tex2;

varying vec2 v_uv1;
varying vec2 v_uv2;


vec4 texture2Dmirror (sampler2D tex, vec2 uv) {
    uv = mod(uv, 2.0);
    uv = mix(uv, 2.0 - uv, step(vec2(1.0), uv));
    return texture2D(tex, fract(uv));
}

vec4 texture2Dclamp (sampler2D tex, vec2 uv) {
    vec4 color = texture2D(tex, uv);
    uv = step(vec2(0.0), uv) * step(uv, vec2(1.0));
    return color * uv[0] * uv[1];
}

void main () {
    vec4 tex1 = texture2Dmirror(u_tex1, v_uv1);
    vec4 tex2 = texture2Dclamp(u_tex2, v_uv2);
    vec4 base = tex2 + tex1 * (1.0 - tex2.a);
    gl_FragColor = base;
}