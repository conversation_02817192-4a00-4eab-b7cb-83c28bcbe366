[{"ClassName": "Class", "Super": "", "FilePath": "lua/src/common/Class.lua", "FileAbsPath": "/Users/<USER>/EffectMacaron/scriptEffect/AmazingFeature/lua/src/common/Class.lua", "Properties": [], "Methods": [{"FuncName": "extend ", "Params": [], "Comment": ""}, {"FuncName": "new ", "Params": [], "Comment": ""}, {"FuncName": "isBaseOf ", "Params": [], "Comment": ""}, {"FuncName": "isInstance ", "Params": [], "Comment": ""}], "Comment": "", "ExportFiles": []}, {"ClassName": "Mat3", "Super": "", "FilePath": "lua/src/common/Mat3.lua", "FileAbsPath": "/Users/<USER>/EffectMacaron/scriptEffect/AmazingFeature/lua/src/common/Mat3.lua", "Properties": [], "Methods": [{"FuncName": "identity ", "Params": [], "Comment": ""}, {"FuncName": "rotate ", "Params": [], "Comment": ""}, {"FuncName": "translate ", "Params": [], "Comment": ""}, {"FuncName": "scale ", "Params": [], "Comment": ""}, {"FuncName": "skewX ", "Params": [], "Comment": ""}, {"FuncName": "skewY ", "Params": [], "Comment": ""}, {"FuncName": "determinant ", "Params": [], "Comment": ""}, {"FuncName": "inverse ", "Params": [], "Comment": ""}, {"FuncName": "mulMatrix ", "Params": [], "Comment": ""}, {"FuncName": "mulVector ", "Params": [], "Comment": ""}, {"FuncName": "mulPoint ", "Params": [], "Comment": ""}, {"FuncName": "mulScalar ", "Params": [], "Comment": ""}, {"FuncName": "toMaterial ", "Params": [], "Comment": ""}, {"FuncName": "toMaterialVector ", "Params": [], "Comment": ""}, {"FuncName": "makeTransform ", "Params": [{"ParaName": "screenSize", "ParaType": "Vector2f|nil", "Comment": ""}, {"ParaName": "layers", "ParaType": "table[]", "Comment": ""}], "Comment": "", "Return": {"Types": ["Mat3"], "Comment": ""}}], "Comment": "", "ExportFiles": []}, {"ClassName": "Setting", "Super": "", "FilePath": "lua/src/common/Setting.lua", "FileAbsPath": "/Users/<USER>/EffectMacaron/scriptEffect/AmazingFeature/lua/src/common/Setting.lua", "Properties": [], "Methods": [{"FuncName": "new ", "Params": [{"ParaName": "map", "ParaType": "Map", "Comment": ""}], "Comment": ""}, {"FuncName": "__index ", "Params": [], "Comment": ""}, {"FuncName": "__newindex ", "Params": [], "Comment": ""}], "Comment": "", "ExportFiles": []}, {"ClassName": "exposure_script", "Super": "ScriptComponent", "FilePath": "effects/Exposure/lua/exposure_script.lua", "FileAbsPath": "/Users/<USER>/EffectMacaron/scriptEffect/AmazingFeature/effects/Exposure/lua/exposure_script.lua", "Properties": [{"VarName": "exposure", "VarType": "Double", "Comment": ""}, {"VarName": "offset", "VarType": "Double", "Comment": ""}, {"VarName": "grayscale_correct", "VarType": "Double", "Comment": ""}, {"VarName": "no_use_linear_light", "VarType": "Bool", "Comment": ""}, {"VarName": "InputTex", "VarType": "Texture", "Comment": ""}, {"VarName": "OutputTex", "VarType": "Texture", "Comment": ""}], "Methods": [{"FuncName": "new", "Params": [], "Comment": ""}, {"FuncName": "onStart", "Params": [{"ParaName": "comp", "ParaType": "Component", "Comment": ""}], "Comment": ""}, {"FuncName": "start", "Params": [], "Comment": ""}, {"FuncName": "onUpdate", "Params": [{"ParaName": "comp", "ParaType": "Component", "Comment": ""}, {"ParaName": "deltaTime", "ParaType": "Double", "Comment": ""}], "Comment": ""}], "Comment": "", "ExportFiles": []}, {"ClassName": "G<PERSON>sianBlurLayerScript", "Super": "ScriptComponent", "FilePath": "effects/GaussianBlur/lua/GaussianBlurLayerScript.lua", "FileAbsPath": "/Users/<USER>/EffectMacaron/scriptEffect/AmazingFeature/effects/GaussianBlur/lua/GaussianBlurLayerScript.lua", "Properties": [{"VarName": "intensity", "VarType": "Double", "Comment": ""}, {"VarName": "steps", "VarType": "Double", "Comment": ""}, {"VarName": "Blurred_Direction", "VarType": "string[UI(Option={\"Horizontal", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Option", "RawValue": "", "Values": ["Horizontal and Vertical", "Horizontal", "Vertical"]}]}]}, {"VarName": "MidTex", "VarType": "Texture", "Comment": ""}, {"VarName": "InputTex", "VarType": "Texture", "Comment": ""}, {"VarName": "OutputTex", "VarType": "Texture", "Comment": ""}], "Methods": [{"FuncName": "new", "Params": [], "Comment": ""}, {"FuncName": "onStart", "Params": [{"ParaName": "comp", "ParaType": "Component", "Comment": ""}], "Comment": ""}, {"FuncName": "setEffectAttr", "Params": [], "Comment": ""}, {"FuncName": "onUpdate", "Params": [{"ParaName": "comp", "ParaType": "Component", "Comment": ""}, {"ParaName": "deltaTime", "ParaType": "Double", "Comment": ""}], "Comment": ""}], "Comment": "", "ExportFiles": []}]