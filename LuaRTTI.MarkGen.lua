
Class = Class or {} ---@type Class
Class.__typename = "Class"
Class.__supername = ""
Class.__scriptname = ""
Class.__scriptpath = ""


Mat3 = Mat3 or {} ---@type Mat3
Mat3.__typename = "Mat3"
Mat3.__supername = ""
Mat3.__scriptname = ""
Mat3.__scriptpath = ""


Setting = Setting or {} ---@type Setting
Setting.__typename = "Setting"
Setting.__supername = ""
Setting.__scriptname = ""
Setting.__scriptpath = ""


exposure_script = exposure_script or {} ---@type exposure_script
exposure_script.__typename = "exposure_script"
exposure_script.__supername = "ScriptComponent"
exposure_script.__scriptname = ""
exposure_script.__scriptpath = ""


GaussianBlurLayerScript = GaussianBlurLayerScript or {} ---@type GaussianBlurLayerScript
GaussianBlurLayerScript.__typename = "GaussianBlurLayerScript"
GaussianBlurLayerScript.__supername = "ScriptComponent"
GaussianBlurLayerScript.__scriptname = ""
GaussianBlurLayerScript.__scriptpath = ""

